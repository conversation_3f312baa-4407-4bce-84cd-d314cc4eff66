# Django API Practice - Render.com Deployment Guide

## Overview
This guide will help you deploy your Django REST API to Render.com using PostgreSQL as the database.

## Changes Made for Deployment

### 1. Updated Dependencies (`mysite/requirements.txt`)
- Added `psycopg2-binary` for PostgreSQL support
- Added `dj-database-url` for database URL parsing
- Added `whitenoise[brotli]` for static file serving
- Added `gunicorn` and `uvicorn` for production server
- Removed `mysqlclient` (replaced with PostgreSQL)

### 2. Modified Settings (`mysite/mysite/settings.py`)
- **Security**: SECRET_KE<PERSON> now uses environment variable
- **Debug**: Automatically disabled in production (when RENDER env var exists)
- **Database**: Switched from MySQL to PostgreSQL using DATABASE_URL
- **Static Files**: Added WhiteNoise middleware for static file serving
- **Allowed Hosts**: Added support for Render hostname

### 3. Created Build Script (`mysite/build.sh`)
- Installs dependencies
- Collects static files
- Runs database migrations

### 4. Created Infrastructure Configuration (`render.yaml`)
- Defines PostgreSQL database
- Configures web service
- Sets environment variables

## Deployment Steps

### Option 1: Using render.yaml (Recommended)

1. **Push to Git Repository**
   ```bash
   git add .
   git commit -m "Configure for Render deployment"
   git push origin main
   ```

2. **Deploy on Render**
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New Blueprint Instance"
   - Connect your repository
   - Give your blueprint a name
   - Click "Apply"

### Option 2: Manual Deployment

1. **Create PostgreSQL Database**
   - Go to Render Dashboard
   - Create new PostgreSQL database
   - Copy the internal database URL

2. **Create Web Service**
   - Create new Web Service
   - Connect your repository
   - Set Language: `Python 3`
   - Set Root Directory: `mysite`
   - Set Build Command: `./build.sh`
   - Set Start Command: `python -m gunicorn mysite.asgi:application -k uvicorn.workers.UvicornWorker`

3. **Set Environment Variables**
   - `DATABASE_URL`: Your PostgreSQL internal URL
   - `SECRET_KEY`: Generate a secure random value
   - `WEB_CONCURRENCY`: `4`

## Post-Deployment

### Create Django Admin User
Once deployed, create an admin user via Render Shell:
```bash
python manage.py createsuperuser
```

### API Endpoints
Your API will be available at:
- `https://your-app.onrender.com/blogposts/` - List/Create blog posts
- `https://your-app.onrender.com/blogposts/{id}/` - Retrieve/Update/Delete specific post

## Local Development
For local development, you can still use SQLite by setting up a local `.env` file or keeping the original database configuration for development.

## Troubleshooting

### Common Issues:
1. **Build Failures**: Check that all dependencies are in requirements.txt
2. **Database Errors**: Ensure DATABASE_URL is correctly set
3. **Static Files**: Make sure WhiteNoise is properly configured
4. **Secret Key**: Ensure SECRET_KEY environment variable is set

### Logs:
- Check deployment logs in Render Dashboard
- Use Render Shell for debugging: `python manage.py shell`

## Security Notes
- SECRET_KEY is now environment-based
- DEBUG is automatically disabled in production
- Database credentials are managed by Render
- Static files are served securely via WhiteNoise

Your Django API is now ready for production deployment on Render.com!

# Environment Variables Setup Guide

## Overview
This Django project uses `python-dotenv` to manage environment variables for different deployment environments.

## Local Development Setup

### 1. Copy Environment Template
```bash
cp .env.example .env
```

### 2. Configure Your Local Environment
Edit `.env` file with your settings:

```env
# Database Configuration
# For Render PostgreSQL (production):
DATABASE_URL=postgresql://admin_user:<EMAIL>/django_api_0m6e

# For local PostgreSQL:
# DATABASE_URL=postgresql://postgres:your_password@localhost:5432/mysite

# For local SQLite (fallback):
# DATABASE_URL=sqlite:///db.sqlite3

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
```

## Environment Variable Behavior

### DATABASE_URL
- **With .env**: Uses the URL specified in `.env` file
- **Without .env**: Falls back to `postgresql://postgres:1234@localhost:5432/mysite`
- **On Render**: Automatically uses Render's DATABASE_URL environment variable

### SECRET_KEY
- **With .env**: Uses the key from `.env` file
- **Without .env**: Uses Django's default insecure key
- **On Render**: Uses generated secure key from Render environment

### DEBUG
- **With .env**: Uses value from `.env` (True/False)
- **Without .env**: Automatically `False` on Render, `True` locally
- **On Render**: Always `False` for security

## Different Environment Configurations

### Local Development with Render Database
```env
DATABASE_URL=postgresql://admin_user:<EMAIL>/django_api_0m6e
SECRET_KEY=django-insecure-36%m+hms#0f6$o*)#iqlqgls2ufoqu=(36m)e83$a$^-ikn$fo
DEBUG=True
```

### Local Development with Local PostgreSQL
```env
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/mysite
SECRET_KEY=django-insecure-36%m+hms#0f6$o*)#iqlqgls2ufoqu=(36m)e83$a$^-ikn$fo
DEBUG=True
```

### Local Development with SQLite
```env
DATABASE_URL=sqlite:///db.sqlite3
SECRET_KEY=django-insecure-36%m+hms#0f6$o*)#iqlqgls2ufoqu=(36m)e83$a$^-ikn$fo
DEBUG=True
```

## Testing Environment Variables

### Check Current Configuration
```bash
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('DATABASE_URL:', os.getenv('DATABASE_URL', 'Not found - will use localhost'))
print('SECRET_KEY:', 'Found' if os.getenv('SECRET_KEY') else 'Not found')
print('DEBUG:', os.getenv('DEBUG', 'Not found - will use default'))
"
```

### Test Database Connection
```bash
python manage.py check --database default
```

### Run Migrations
```bash
python manage.py migrate
```

## Production Deployment (Render)

### Environment Variables on Render
Set these in Render Dashboard or render.yaml:

```yaml
envVars:
  - key: DATABASE_URL
    value: ***************************************************************************************************
  - key: SECRET_KEY
    generateValue: true
  - key: WEB_CONCURRENCY
    value: 4
```

## Security Notes

### .env File Security
- ✅ `.env` is in `.gitignore` - never commit it
- ✅ Use `.env.example` as template for team members
- ✅ Different `.env` for different environments

### Production Security
- ✅ `DEBUG=False` automatically on Render
- ✅ `SECRET_KEY` generated securely by Render
- ✅ Database credentials managed by Render

## Troubleshooting

### Common Issues

1. **ModuleNotFoundError: No module named 'dotenv'**
   ```bash
   pip install python-dotenv
   ```

2. **Database connection failed**
   - Check DATABASE_URL format
   - Verify database credentials
   - Test with `python manage.py check --database default`

3. **Environment variables not loading**
   - Ensure `.env` file is in the same directory as `manage.py`
   - Check file permissions
   - Verify no syntax errors in `.env`

### Debug Environment Loading
```python
# Add to settings.py temporarily for debugging
print("Current working directory:", os.getcwd())
print("DATABASE_URL:", os.getenv('DATABASE_URL'))
print("DEBUG:", os.getenv('DEBUG'))
```

## Best Practices

1. **Never commit `.env` files**
2. **Use different `.env` for different environments**
3. **Keep `.env.example` updated**
4. **Use secure, unique SECRET_KEY for production**
5. **Set DEBUG=False in production**
6. **Use environment-specific database URLs**

Your Django application now supports flexible environment configuration! 🚀

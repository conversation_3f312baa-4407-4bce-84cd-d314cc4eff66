databases:
  - name: django-api-db
    plan: free
    databaseName: django_api
    user: django_api_user

services:
  - type: web
    plan: free
    name: django-api-practice
    runtime: python
    rootDir: ./mysite
    buildCommand: './build.sh'
    startCommand: 'python -m gunicorn mysite.asgi:application -k uvicorn.workers.UvicornWorker'
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: django-api-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: WEB_CONCURRENCY
        value: 4

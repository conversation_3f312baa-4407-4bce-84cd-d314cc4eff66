# PostgreSQL Setup Guide for Render.com

## Overview
This comprehensive guide covers setting up and managing PostgreSQL databases on Render.com, from creation to production optimization.

## Table of Contents
1. [Creating Your Database](#creating-your-database)
2. [Connection Methods](#connection-methods)
3. [Configuration for Django](#configuration-for-django)
4. [Security & Access Control](#security--access-control)
5. [Storage Management](#storage-management)
6. [Performance Optimization](#performance-optimization)
7. [Monitoring & Maintenance](#monitoring--maintenance)
8. [Troubleshooting](#troubleshooting)

## Creating Your Database

### Step 1: Access Database Creation
1. Go to [dashboard.render.com/new/database](https://dashboard.render.com/new/database)
2. Or click **+ New > Postgres** in the Render Dashboard

### Step 2: Configure Database Settings
- **Name**: Choose a descriptive name (e.g., `django-api-db`)
- **Database**: Specify database name (e.g., `django_api`) - cannot be changed later
- **User**: Set username (e.g., `django_api_user`) - cannot be changed later
- **Region**: Choose same region as your web services for optimal performance
- **PostgreSQL Version**: Select version (13-17 available, 17 recommended)

### Step 3: Select Instance Type
**Free Tier Limitations:**
- 1 GB storage (fixed)
- 97 connections max
- No backups
- May sleep after 90 days of inactivity

**Paid Plans Benefits:**
- Scalable storage (1GB to 4TB+)
- More connections (100-500 based on RAM)
- Point-in-time recovery
- Read replicas (larger instances)
- High availability options

### Step 4: Set Initial Storage
- Minimum: 1 GB
- Increments: Multiples of 5 GB
- Note: Storage can be increased but never decreased

## Connection Methods

### Internal URL (Recommended)
**Format:** `postgresql://USER:PASSWORD@INTERNAL_HOST:PORT/DATABASE`

**When to use:**
- Connecting from Render services in the same region
- Faster performance (private network)
- Lower latency

**Example:**
```
**********************************************************/django_api
```

### External URL
**Format:** `postgresql://USER:PASSWORD@EXTERNAL_HOST:PORT/DATABASE`

**When to use:**
- Local development
- External tools (pgAdmin, DBeaver)
- CI/CD pipelines
- Third-party services

**Example:**
```
postgresql://django_api_user:<EMAIL>:5432/django_api
```

## Configuration for Django

### 1. Install Required Packages
```bash
pip install psycopg2-binary dj-database-url
```

### 2. Update settings.py
```python
import dj_database_url
import os

# Database configuration
DATABASES = {
    'default': dj_database_url.config(
        default='postgresql://postgres:postgres@localhost:5432/mysite',
        conn_max_age=600
    )
}
```

### 3. Environment Variables
Set in Render Dashboard or render.yaml:
```yaml
envVars:
  - key: DATABASE_URL
    fromDatabase:
      name: django-api-db
      property: connectionString
```

### 4. Local Development Setup
Create `.env` file:
```
DATABASE_URL=postgresql://username:password@localhost:5432/local_db
```

## Security & Access Control

### Default Security
- Encrypted at rest (AES-256)
- TLS encryption in transit
- Render-managed certificates

### IP Restrictions
1. Go to database **Info** page
2. Scroll to **Access Control** section
3. Add IP addresses using CIDR notation:
   - Single IP: `***********/32`
   - IP range: `***********/24`
   - Disable external access: Remove all IPs

### Connection Limits
| Memory | Max Connections |
|--------|----------------|
| < 8GB  | 100           |
| 8-16GB | 200           |
| 16-32GB| 400           |
| ≥ 32GB | 500           |

## Storage Management

### Monitoring Storage
- Check **Metrics** page in dashboard
- Set up alerts for storage usage
- Monitor disk usage trends

### Increasing Storage
1. Go to database **Info** page
2. Click **Update** in PostgreSQL Instance section
3. Increase storage (multiples of 5GB)
4. Click **Save Changes**

**Important Notes:**
- No downtime for storage increases
- Cannot decrease storage
- 12-hour cooldown between increases

### Storage Emergency
If database exceeds storage limit:
1. Database becomes unhealthy and suspended
2. Click **Resume Database** in dashboard
3. Immediately increase storage
4. Database becomes healthy within minutes

## Performance Optimization

### Connection Pooling
For high-traffic applications:
```python
# settings.py
DATABASES = {
    'default': {
        # ... other settings
        'CONN_MAX_AGE': 600,  # 10 minutes
        'OPTIONS': {
            'MAX_CONNS': 20,
        }
    }
}
```

### Query Optimization
- Monitor slow queries (>2 seconds logged)
- Use database indexes appropriately
- Implement query caching
- Consider read replicas for read-heavy workloads

### Instance Scaling
Upgrade instance type for:
- More RAM (better caching)
- More CPU (faster queries)
- More connections
- High availability features

## Monitoring & Maintenance

### Built-in Metrics
Available in Render Dashboard:
- Disk usage
- Active connections
- Query performance
- Memory usage
- CPU utilization

### Slow Query Monitoring
Queries >2 seconds are automatically logged:
```
duration: 3456.789 ms statement: SELECT * FROM large_table WHERE...
```

### Backup Strategy
**Paid Plans:**
- Automatic point-in-time recovery
- On-demand logical exports
- Continuous backup to AWS S3

**Free Plans:**
- Manual backups using pg_dump
- Export before major changes

### Database Maintenance
```sql
-- Connect via psql
-- Analyze database performance
ANALYZE;

-- Vacuum to reclaim space
VACUUM;

-- Check database size
SELECT pg_size_pretty(pg_database_size('your_database_name'));

-- Monitor active connections
SELECT count(*) FROM pg_stat_activity;
```

## Troubleshooting

### Common Connection Issues
1. **SSL Errors**: Ensure client supports TLS 1.2+
2. **Connection Refused**: Check IP restrictions
3. **Too Many Connections**: Implement connection pooling
4. **Timeout Issues**: Use internal URL for Render services

### Performance Issues
1. **Slow Queries**: Check logs for queries >2 seconds
2. **High Memory Usage**: Consider upgrading instance
3. **Connection Limits**: Upgrade instance or implement pooling

### Django-Specific Issues
```python
# Test database connection
python manage.py dbshell

# Check migrations
python manage.py showmigrations

# Create superuser
python manage.py createsuperuser
```

### Emergency Procedures
1. **Database Suspended**: Resume and increase storage immediately
2. **Connection Issues**: Check access control and credentials
3. **Performance Problems**: Monitor metrics and consider scaling

## Best Practices

### Development Workflow
1. Use external URL for local development
2. Use internal URL for production services
3. Test migrations on staging database first
4. Regular backup exports for critical data

### Production Deployment
1. Enable high availability for critical applications
2. Set up monitoring and alerts
3. Implement connection pooling
4. Regular performance reviews
5. Plan for storage growth

### Cost Optimization
1. Start with appropriate instance size
2. Monitor actual usage patterns
3. Scale up only when needed
4. Use read replicas for read-heavy workloads
5. Implement efficient queries and indexing

## Additional Resources
- [Render PostgreSQL Extensions](https://render.com/docs/postgresql-extensions)
- [High Availability Setup](https://render.com/docs/postgresql-high-availability)
- [Read Replicas Guide](https://render.com/docs/postgresql-read-replicas)
- [Performance Troubleshooting](https://render.com/docs/postgresql-performance-troubleshooting)

Your PostgreSQL database on Render is now ready for production use with proper monitoring, security, and scalability considerations!

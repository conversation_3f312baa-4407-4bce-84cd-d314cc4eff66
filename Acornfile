args: {
	djangodbname: "djangodb"
}

services: db: {
	image: "ghcr.io/acorn-io/mariadb:v10.11.5-1"
	serviceArgs: {
		dbName: args.djangodbname
	}
}

jobs: {
	dbinit: {
		build: images.app.containerBuild
		env: {
			MARIADB_USER:          "@{service.db.secrets.admin.username}"
			MARIADB_ROOT_PASSWORD: "@{service.db.secrets.admin.password}"
			MARIADB_HOST:          "@{service.db.address}"
			MARIADB_PORT:          "@{service.db.port.3306}"
			MARIADB_DATABASE:      "@{service.db.data.dbName}"
		}
		consumes: ["db"]
		entrypoint: "/bin/bash"
		command:    "-c 'chmod +x ./db-script.sh && ./db-script.sh'"
	}
}

containers: web: {
	build: images.app.containerBuild
	ports: publish: "8000:8000/http"
	dependsOn: ["dbinit"]
	consumes: ["db"]

	if args.dev {dirs: "/app": "./mysite"}

	env: {
		MARIADB_USER:          "@{service.db.secrets.admin.username}"
		MARIADB_ROOT_PASSWORD: "@{service.db.secrets.admin.password}"
		MARIADB_HOST:          "@{service.db.address}"
		MARIADB_PORT:          "@{service.db.port.3306}"
		MARIADB_DATABASE:      "@{service.db.data.dbName}"
	}
}

images: app: containerBuild: {
	context:    "./mysite"
	dockerfile: "./mysite/Dockerfile"
}